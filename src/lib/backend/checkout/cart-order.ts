import { SiteCartOrderRequest } from '~/data/models/SiteCartOrderRequest';
import { SiteCartOrderResponse } from '~/data/models/SiteCartOrderResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendCreateOrder(
  input: SiteCartOrderRequest,
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartOrderResponse,
    SiteCartOrderRequest
  >({
    endpoint: '/v2/site/orders',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'post',
  });
}

export async function backendGetOrder({
  extraQueryParams,
  orderId,
  query,
}: {
  extraQueryParams?: Record<string, string>;
  orderId: string;
  query?: Record<string, string>;
}) {
  return await fetchWithErrorHandling<SiteCartOrderResponse>({
    endpoint: '/v2/site/orders/{orderId}',
    extraQueryParams,
    includeAuthorization: true,
    method: 'get',
    params: {
      orderId,
    },
    query,
  });
}
