'use client';

import lscache from 'lscache';
import { useEffect } from 'react';

import Grid from '~/components/global/Grid/Grid';
import GridItem from '~/components/global/Grid/GridItem';
import Icon from '~/components/global/Icon/Icon';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import { formatHours } from '~/components/global/InstallationShopDetails/InstallationShopDetails.utils';
import { useElement } from '~/components/global/SyncScroll/useElement';
import { useCartShippingContextSelector } from '~/components/modules/Cart/CartShipping.context';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import { OrderTrackingResultContextProvider } from '~/components/pages/OrderTrackingResult/OrderTrackingResult.context';
import { useFooterContextSelector } from '~/context/Footer.context';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { useWidgetConfigContextSelector } from '~/context/WidgetConfig.context';
import { SiteCartBillingResponse } from '~/data/models/SiteCartBillingResponse';
import { SiteCartCouponItem } from '~/data/models/SiteCartCouponItem';
import { SiteCartOrderResponse } from '~/data/models/SiteCartOrderResponse';
import {
  ShippingType,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { SiteInstallLocation } from '~/data/models/SiteInstallLocation';
import { useApiDataWithDefault } from '~/hooks/useApiDataWithDefault';
import useBreakpoints from '~/hooks/useBreakpoints';
import useEffectOnlyOnce from '~/hooks/useEffectOnlyOnce';
import { apiSendRudderTrackEvent } from '~/lib/api/rudder-event-trigger';
import { FS_EVENT_NAMES } from '~/lib/constants/fullstory';
import { LOCAL_STORAGE, PROPERTIES } from '~/lib/constants/localStorage';
import {
  PROPERTIES as SESSION_PROPERTIES,
  SESSION_STORAGE,
} from '~/lib/constants/sessionStorage';
import { USER_TYPE } from '~/lib/constants/sso';
import { SSOUserIdResponse } from '~/lib/constants/sso.types';
import { TIME } from '~/lib/constants/time';
import { setFSCustomEvent } from '~/lib/helpers/fullstory';
import logger from '~/lib/helpers/logger';
import {
  rudderstackSendIdentifyEvent,
  rudderstackSendTrackEvent,
} from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import {
  mapToCartObject,
  mapToOrderBrand,
  mapToOrderImageUrl,
  mapToOrderName,
  mapToOrderPdpUrl,
  mapToOrderQty,
  mapToOrdersCost,
  mapToOrderType,
  mapToOrderVariant,
  mapToShippingRecipient,
} from '~/lib/helpers/rudderstack/transformer';
import { ExtendedEventProperties } from '~/lib/helpers/rudderstack/types';
import { seStorage } from '~/lib/utils/browser-storage';
import { isOTSDeployment, isSimpleShopDeployment } from '~/lib/utils/deploy';
import { ui } from '~/lib/utils/ui-dictionary';

import ErrorPage from '../../ErrorPage/ErrorPage';
import { LOGO_PATHS } from '../checkout.constants';
import { ServerData as OrderSummaryServerData } from '../OrderSummary/OrderSummary.types';
import CelebrationHeader from './CelebrationHeader/CelebrationHeader';
import CreateAccount from './CreateAccount/CreateAccount';
import Footer from './Footer/Footer';
import {
  shippingLocationStyles,
  styles,
} from './OrderConfirmationContainer.styles';
import OrderSummarySection from './OrderSummarySection/OrderSummarySection';
import WhatHappens from './WhatHappens/WhatHappens';

function ShippingLocation({
  siteShipping,
  cartInstallLocation,
}: {
  cartInstallLocation: SiteInstallLocation | undefined;
  siteShipping: SiteCartShippingResponse;
}) {
  const { cartShipping, shippingOption } = siteShipping;
  const { shippingLocation, installer } = cartShipping;
  const isOTS = isOTSDeployment();
  const installerNameOTS = seStorage.getItem(
    SESSION_STORAGE[SESSION_PROPERTIES.INSTALLER_COMPANY],
  );
  const companyName = isOTS
    ? installerNameOTS
    : cartShipping?.installer?.company || '';
  const isMobileInstall = cartInstallLocation?.addressLine1 ? true : false;

  let addressLine1 = cartShipping.addressLine1;
  if (shippingOption === ShippingType.FEDEX && shippingLocation) {
    addressLine1 = shippingLocation.addressLine1;
  }

  let addressLine2 = cartShipping.addressLine2;
  if (shippingOption === ShippingType.FEDEX && shippingLocation) {
    addressLine2 = shippingLocation.addressLine2;
  }
  if (shippingOption === ShippingType.INSTALLER && installer) {
    addressLine2 = installer.addressLine2;
  }

  if (cartInstallLocation?.addressLine1) {
    addressLine1 = cartInstallLocation.addressLine1;
  }

  if (cartInstallLocation?.addressLine2) {
    addressLine2 = cartInstallLocation.addressLine2;
  }

  const addressInfo =
    addressLine2 && addressLine2.length > 0
      ? `${addressLine1} ${addressLine2}`
      : addressLine1;
  let city = cartShipping.city;
  if (shippingOption === ShippingType.FEDEX && shippingLocation) {
    city = shippingLocation.city;
  }
  if (shippingOption === ShippingType.INSTALLER && installer) {
    city = installer.city;
  }
  if (cartInstallLocation?.city) {
    city = cartInstallLocation.city;
  }

  let state = cartShipping.state;
  if (shippingOption === ShippingType.FEDEX && shippingLocation) {
    state = shippingLocation.state;
  }
  if (shippingOption === ShippingType.INSTALLER && installer) {
    state = installer.state;
  }
  if (cartInstallLocation?.state) {
    state = cartInstallLocation.state;
  }

  let zip = cartShipping.zip;
  if (shippingOption === ShippingType.FEDEX && shippingLocation) {
    zip = shippingLocation.zip;
  }
  if (shippingOption === ShippingType.INSTALLER && installer) {
    zip = installer.zip;
  }
  if (cartInstallLocation?.zip) {
    zip = cartInstallLocation.zip;
  }

  return (
    <div>
      <p css={shippingLocationStyles.title}>
        {ui('checkout.orderConfirm.shippingLocation.title')}
      </p>
      <div css={shippingLocationStyles.root}>
        <Icon name={ICONS.FREE_SHIPPING} css={shippingLocationStyles.icon} />
        <div css={shippingLocationStyles.addressContainer}>
          <span css={shippingLocationStyles.addressTitle}>
            {`${companyName} ${
              shippingOption === ShippingType.INSTALLER && isMobileInstall
                ? 'Mobile Installation'
                : ''
            }`}
          </span>
          {!isMobileInstall && (
            <span>
              <span css={shippingLocationStyles.addressInfo}>
                {addressInfo},&nbsp;
              </span>
              <span css={shippingLocationStyles.cityStateZip}>
                {city},&nbsp;{state}&nbsp;{zip}
              </span>
            </span>
          )}
        </div>
      </div>
      {shippingOption === ShippingType.INSTALLER && isMobileInstall && (
        <p css={shippingLocationStyles.title}>
          {ui('checkout.orderConfirm.mobileShippingLocation.title', {
            installer: companyName ?? '',
          })}
        </p>
      )}
      {shippingOption === ShippingType.INSTALLER && isMobileInstall && (
        <div css={shippingLocationStyles.root}>
          {
            <Icon
              name={ICONS.VEHICLE_HEAVY_TRUCK}
              css={shippingLocationStyles.icon}
            />
          }
          <div css={shippingLocationStyles.addressContainer}>
            <span css={shippingLocationStyles.addressTitle}>
              {ui('checkout.orderConfirm.mobileShippingLocation.location')}
            </span>
            <span css={shippingLocationStyles.addressInfo}>
              {addressInfo},&nbsp;
            </span>
            <span css={shippingLocationStyles.cityStateZip}>
              {city},&nbsp;{state}&nbsp;{zip}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export interface ServerData extends OrderSummaryServerData {
  cartId: string;
  orderId: string;
  orderNo?: number;
  setCookie: () => Promise<void>;
  siteCartOrder: SiteCartOrderResponse | null;
  userDetails: SSOUserIdResponse | null;
}

function OrderConfirmationContainer({
  siteShipping,
  siteBilling,
  siteCartOrder,
  userDetails,
  cartId,
  orderId,
  orderNo,
  siteCartSummaryData,
  setCookie,
}: ServerData) {
  const { lessThan } = useBreakpoints();
  const { isDealerTire, userPersonalizationData } =
    useUserPersonalizationContextSelector((v) => ({
      isDealerTire: v.isDealerTire,
      userPersonalizationData: v.userPersonalizationData,
    }));
  const widgetAppConfig = useWidgetConfigContextSelector(
    (v) => v.widgetAppConfig,
  );
  const isOTS = useGlobalsContextSelector((v) => Number(v.isOTS) === 1);
  const { cartSummary, currentOrderCancelled } = useCartSummaryContextSelector(
    (v) => ({
      cartSummary: v.cartSummary,
      currentOrderCancelled: v.currentOrderCancelled,
    }),
  );
  const { cartAppointment, cartInstallLocation } =
    useCartShippingContextSelector((v) => ({
      cartAppointment: v.cartAppointment,
      cartInstallLocation: v.cartInstallLocation,
    }));

  const cleanupCart = useCartUserActionContextSelector((v) => v.cleanupCart);
  const { userType, isFleet } = useUserPersonalizationContextSelector((v) => ({
    userType: v.userType,
    isFleet: v.isFleet,
  }));

  const [createAccountElement, createAccountRef] = useElement();
  const email = siteShipping?.cartShipping.email || cartSummary?.email || '';
  const installerNameOTS = seStorage.getItem(
    SESSION_STORAGE[SESSION_PROPERTIES.INSTALLER_COMPANY],
  );

  const isLoggedIn = !!userDetails;

  const setIsFooterVisible = useFooterContextSelector(
    (v) => v.setIsFooterVisible,
  );

  const { data: orderData } = useApiDataWithDefault<
    SiteCartOrderResponse | undefined
  >({
    defaultData: siteCartOrder ?? undefined,
    endpoint: '/checkout/cart-order',
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserTime: true,
    includeUserZip: true,
    options: {
      onError: (error) => {
        logger.error(`cart order[${orderId}]  api error:`, error);
      },
    },
    query: { id: orderId },
  });

  const { order, customer, emailMatchesExistingAccount } = orderData ?? {};
  const { fname } = customer ?? {};

  const { data: shippingData } =
    useApiDataWithDefault<SiteCartShippingResponse | null>({
      defaultData: siteShipping ?? null,
      endpoint: '/checkout/cart-shipping',
      includeAuthorization: true,
      includeUserRegion: true,
      includeUserZip: true,
      query: { cartId },
    });

  const { data: billingData } =
    useApiDataWithDefault<SiteCartBillingResponse | null>({
      defaultData: siteBilling ?? null,
      endpoint: '/checkout/cart-billing',
      includeAuthorization: true,
      includeUserRegion: true,
      includeUserZip: true,
      query: { cartId },
    });

  const { data: cartSummaryData } =
    useApiDataWithDefault<SiteCartSummaryResponse | null>({
      defaultData: siteCartSummaryData ?? null,
      endpoint: '/cart-summary',
      includeAuthorization: true,
      includeUserRegion: true,
      includeUserSSOUid: true,
      includeUserTime: true,
      includeUserZip: true,
      query: {
        id: cartId,
      },
    });

  const isWarehousePickup =
    cartSummaryData?.siteCart?.shippingType != undefined &&
    cartSummaryData?.siteCart?.shippingType == ShippingType.WAREHOUSE;

  const warehouseAddress =
    siteCartOrder?.warehouseDetails?.address2 &&
    siteCartOrder?.warehouseDetails?.address2.length > 0
      ? `${siteCartOrder?.warehouseDetails?.name},
      ${siteCartOrder?.warehouseDetails?.address1}, ${siteCartOrder?.warehouseDetails?.address2},
      ${siteCartOrder?.warehouseDetails?.city}, ${siteCartOrder?.warehouseDetails?.state},
      ${siteCartOrder?.warehouseDetails?.zip}`
      : `${siteCartOrder?.warehouseDetails?.name},
      ${siteCartOrder?.warehouseDetails?.address1}, ${siteCartOrder?.warehouseDetails?.city},
      ${siteCartOrder?.warehouseDetails?.state}, ${siteCartOrder?.warehouseDetails?.zip}`;

  const purchaseDateTime =
    siteCartOrder &&
    siteCartOrder?.order &&
    siteCartOrder?.order?.orderDate &&
    new Date(
      new Date(siteCartOrder?.order?.orderDate).getTime() + 60 * 60 * 24 * 1000,
    );

  const purchaseDate = purchaseDateTime?.toLocaleString([], {
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
  if (siteCartOrder) {
    orderNo = siteCartOrder.order.orderNo;
  }

  useEffectOnlyOnce(
    () => {
      setCookie();
    },
    {},
    () => true,
  );

  useEffect(() => {
    setIsFooterVisible(false);

    return () => {
      setIsFooterVisible(true);
    };
  }, [setIsFooterVisible]);

  useEffect(() => {
    if (siteCartOrder && siteShipping === null && siteBilling === null) {
      if (isSimpleShopDeployment()) {
        seStorage.removeItem(
          SESSION_STORAGE[SESSION_PROPERTIES.SELECTED_SERVICES],
        );
        seStorage.removeItem(
          SESSION_STORAGE[SESSION_PROPERTIES.INSTALLER_ID_SELECTED],
        );
        seStorage.removeItem(
          SESSION_STORAGE[SESSION_PROPERTIES.PRE_SELECTED_INSTALLER_TYPE],
        );
        seStorage.removeItem(SESSION_STORAGE[SESSION_PROPERTIES.INSTALLER_IDS]);
        seStorage.removeItem(
          SESSION_STORAGE[SESSION_PROPERTIES.INSTALLER_COMPANY],
        );
        seStorage.removeItem(SESSION_STORAGE[SESSION_PROPERTIES.ADD_TO_CART]);
      }

      window.location.href =
        '/track-your-order/result?orderId=' +
        siteCartOrder.order.orderId +
        '&zip=' +
        siteCartOrder.order.zip;
      return;
    } else {
      const timer = setTimeout(async () => {
        await cleanupCart();
      }, TIME.MS200);
      return () => clearTimeout(timer);
    }
  }, [siteCartOrder, siteShipping, siteBilling, cleanupCart]);

  useEffect(() => {
    if (!siteCartOrder) {
      return;
    }

    // disabling the browser back button
    history.pushState(null, '', window.location.href);
    window.onpopstate = function () {
      history.go(1);
    };
  }, [siteCartOrder]);

  useEffect(() => {
    if (!currentOrderCancelled || !createAccountElement) {
      return;
    }

    setTimeout(() => window.scrollTo(0, createAccountElement.offsetTop));
  }, [createAccountElement, currentOrderCancelled]);

  useEffect(() => {
    if (!siteCartOrder || !cartSummary?.siteCartCoupons) {
      return;
    }

    const couponCodes = cartSummary.siteCartCoupons
      .map((coupon: SiteCartCouponItem) => {
        return coupon.promoCode;
      })
      .join(',');

    setFSCustomEvent(FS_EVENT_NAMES.ORDER_COMPLETED, {
      orderId: siteCartOrder.order.orderId,
      amount: cartSummary?.totalInCents || 0,
      coupon: couponCodes,
    });
  }, [siteCartOrder, cartSummary?.siteCartCoupons, cartSummary?.totalInCents]);

  useEffectOnlyOnce(
    () => {
      if (cartSummary && siteCartOrder && shippingData && siteBilling) {
        const widgetSource = seStorage.getItem(
          SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SOURCE],
        );
        const orderSource = isDealerTire
          ? 'dt-epp'
          : isOTSDeployment()
            ? 'ots-honda'
            : widgetSource === null
              ? 'simpletire'
              : 'widget';
        const widgetName = widgetSource === null ? 'null' : widgetSource;
        let eventPayload: ExtendedEventProperties = {
          billingAddress: siteBilling?.cartBilling.addressLine1 || '',
          billingCity: siteBilling?.cartBilling.city || '',
          billingState: siteBilling?.cartBilling.state || '',
          billingZip: siteBilling?.cartBilling.zip || '',
          checkout_id: cartSummary?.cartUuid ?? '',
          coupon: cartSummary?.siteCartCoupons
            ? { coupons: cartSummary?.siteCartCoupons }
            : '',
          currency: 'USD',
          email,
          estimatedDeliveryDate:
            cartSummary?.shippingDays &&
            parseInt(cartSummary?.shippingDays, 10),
          first_name: siteBilling?.cartBilling.firstName || '',
          installDate: (cartAppointment && cartAppointment.date) || '',
          installDate2: siteCartOrder.order.installationDate || '',
          installDateFinal:
            siteCartOrder.order.installationDate || '1900-01-01 00:00:00',
          installDateFormatted:
            siteCartOrder.order.installationDate || '1900-01-01 00:00:00',
          installer: shippingData?.cartShipping?.installer?.company || '',
          installerAddress:
            shippingData?.cartShipping?.installer?.addressLine1 || '',
          installerCity: shippingData?.cartShipping?.installer?.city || '',
          installerDistance:
            shippingData?.cartShipping?.installer?.distance || '',
          installerImageLink: siteCartOrder.order?.installerImageLink || '',
          installerState: shippingData?.cartShipping?.installer?.state || '',
          installerZip: shippingData?.cartShipping?.installer?.zip || '',
          installTime: cartAppointment?.isDropOff
            ? cartAppointment?.dropOffWindow
            : cartAppointment?.startTime || '',
          last_name: siteBilling?.cartBilling.lastName || '',
          order_id: siteCartOrder.order.orderNo,
          order_source: orderSource,
          orderNumber: siteCartOrder.order.orderNo,
          orderStats: siteCartOrder.orderStats,
          orderType: mapToOrderType(shippingData),
          paymentAccount4: siteCartOrder.order.paymentAccount ?? '',
          paymentMethod: siteCartOrder.order.paymentType ?? '',
          phone:
            (siteBilling?.cartBilling.phone &&
              '+1' + siteBilling?.cartBilling.phone) ||
            '',
          printOrderUrl: `${window.location.origin}/account`,
          products: mapToCartObject(cartSummary),
          purchaseDate: siteCartOrder.order.orderDate ?? '',
          purchaseDate2: siteCartOrder.order.orderDate ?? '',
          revenue: cartSummary?.totalInCents || 0,
          roadsideAssistCost: cartSummary?.roadsideAssistanceCostInCents || 0,
          salesTax: cartSummary?.estimatedTaxInCents || 0,
          savings: cartSummary?.savingTodayInCents || 0,
          shippingAddress: shippingData?.cartShipping?.addressLine1,
          shippingCity: shippingData?.cartShipping?.city,
          shippingCost: cartSummary?.shippingCostInCents || 0,
          shippingRecipient: mapToShippingRecipient(shippingData),
          shippingState: shippingData?.cartShipping?.state,
          shippingZip: shippingData?.cartShipping?.zip,
          stateFees: cartSummary?.estimatedStateFeesInCents || 0,
          subTotalCost: cartSummary?.subTotalInCents || 0,
          tireInstallationCost: cartSummary?.installationCostInCents || 0,
          tiresCost: mapToOrdersCost(cartSummary),
          tiresPurchasedBrand: mapToOrderBrand(cartSummary),
          tiresPurchasedImageUrl: mapToOrderImageUrl(cartSummary),
          tiresPurchasedName: mapToOrderName(cartSummary),
          tiresPurchasedPdpUrl: mapToOrderPdpUrl(cartSummary),
          tiresPurchasedQuantity: mapToOrderQty(cartSummary),
          tiresPurchasedVariant: mapToOrderVariant(cartSummary),
          totalCost: cartSummary?.totalInCents || 0,
          trcAmountCharged:
            (cartSummary?.roadHazardCostInCents ?? 0) > 0
              ? cartSummary?.roadHazardCostInCents || 0
              : cartSummary?.cartRoadHazardCostInCents || 0,
          trcCost: cartSummary?.roadHazardCostInCents || 0,
          trcPurchased: (cartSummary?.roadHazardCostInCents ?? 0) > 0,
          treadType: cartSummary?.siteProducts?.map((product) => {
            return product.productSubType || 'Unknown';
          }) || ['Unknown'],
          urlCancel: `${window.location.origin}/account`,
          urlManageOrder: `${window.location.origin}/account`,
          widget_source: widgetName,
        };

        const zip = userPersonalizationData?.userLocation?.zip;

        const vehicleMetadata =
          lscache.get(LOCAL_STORAGE[PROPERTIES.VEHICLE_METADATA]) || {};

        if (isOTS) {
          const installerHours = widgetAppConfig?.hours;
          const hours = installerHours && formatHours(installerHours);
          const hoursSplit =
            hours && hours.split(',').map((item) => item.trim());
          const hourstext =
            hoursSplit &&
            hoursSplit.reduce((acc, cur) => {
              return acc + cur + '<br>';
            }, '');
          eventPayload = {
            ...eventPayload,
            dealerAddress: `${widgetAppConfig?.address1},${widgetAppConfig?.city}, ${widgetAppConfig?.state}, ${widgetAppConfig?.zip} `,
            dealerBgColor: process.env.NEXT_PUBLIC_OTS_SITE_COLOR || '',
            dealerHours: hourstext,
            dealerInstallAddress: `${widgetAppConfig?.address1},${widgetAppConfig?.city}, ${widgetAppConfig?.state}, ${widgetAppConfig?.zip} `,
            dealerInstallPhone: widgetAppConfig?.phone,
            dealerLogoDark: widgetAppConfig?.logoUrl || '',
            dealerLogoLight: process.env.NEXT_PUBLIC_OTS_LOGO_LIGHT_URL || '',
            dealerName: widgetAppConfig?.name || '',
            dealerPhone: widgetAppConfig?.phone,
          };
        }
        if (widgetSource === 'pirelli') {
          eventPayload = {
            ...eventPayload,
            source: 'pirelli',
            vehicleMake: vehicleMetadata.vehicleMake || 'Unknown',
            vehicleModel: vehicleMetadata.vehicleModel || 'Unknown',
            vehicleTrim: vehicleMetadata.vehicleTrim || 'Unknown',
            vehicleYear: vehicleMetadata.vehicleYear || 'Unknown',
            zip: zip ?? '',
          };
        }

        const userSessionId = lscache.get(LOCAL_STORAGE[PROPERTIES.SESSION]);
        const traitData = {
          first_name: siteBilling?.cartBilling.firstName || '',
          last_name: siteBilling?.cartBilling.lastName || '',
          phone:
            (siteBilling?.cartBilling.phone &&
              '+1' + siteBilling?.cartBilling.phone) ||
            '',
          email,
        };
        rudderstackSendIdentifyEvent(userSessionId, traitData);
        rudderstackSendTrackEvent(
          RudderstackTrackEventName.ORDER_COMPLETED,
          eventPayload,
        );
        setFSCustomEvent(FS_EVENT_NAMES.ORDER_NUMBER, {
          orderNumber: String(siteCartOrder.order.orderNo),
        });
        const payloadPhp = {
          eventPayload,
          userSessionId,
          traitData,
          event: RudderstackTrackEventName.ORDER_COMPLETED_SERVERSIDE,
        };
        (async () => {
          await apiSendRudderTrackEvent({
            input: payloadPhp,
          });
        })();

        // Do not change anything in the following two lines
        window.VWO = window.VWO || [];
        window.VWO.event =
          window.VWO.event ||
          function () {
            // eslint-disable-next-line prefer-rest-params
            window.VWO.push(['event'].concat([].slice.call(arguments)));
          };

        // Replace the property values with your actual values
        window.VWO.event('rudder.ORDER_COMPLETED', {
          billingCity: siteBilling?.cartBilling.city || '',
          billingState: siteBilling?.cartBilling.state || '',
          estimatedDeliveryDate:
            cartSummary?.shippingDays &&
            parseInt(cartSummary?.shippingDays, 10),
          installer: shippingData?.cartShipping?.installer?.company || '',
          orderNumber: siteCartOrder.order.orderId,
          orderType: mapToOrderType(shippingData),
          paymentMethod: siteCartOrder.order.paymentType ?? '',
          revenue: cartSummary?.totalInCents || 0,
        });
        const couponCodes = cartSummary.siteCartCoupons
          ? cartSummary.siteCartCoupons
              .map((coupon: SiteCartCouponItem) => {
                return coupon.promoCode;
              })
              .join(',')
          : [];
        const _talkable_data = {
          purchase: {
            coupon_code: couponCodes, // Coupon code that was used at checkout (pass multiple as an array). Example: 'SAVE20'
            order_number: siteCartOrder.order.orderNo, // Unique order number. Example: '100011'
            shipping_address:
              shippingData?.cartShipping?.addressLine1 +
              ', ' +
              shippingData?.cartShipping?.city +
              ', ' +
              shippingData?.cartShipping?.state +
              ', ' +
              shippingData?.cartShipping?.zip +
              ', USA', // Shipping address in case an event is shippable. It is used in fraud prevention. Example: '2370 Market St, Suite 103, San Francisco, 94114, USA'
            shipping_zip: shippingData?.cartShipping?.zip, // Same as shipping_address. Include only zip here. Example: 94114
            subtotal: cartSummary.subTotalInCents / 100, // Order subtotal (pre-tax, post-discount). Example: '23.97'
          },
          customer: {
            email, // Customer email address who issued a purchase. Example: '<EMAIL>',
            traffic_source: orderSource, // The source of the traffic driven to the campaign. Example: 'facebook'
          },
        };
        window._talkableq = window._talkableq || [];
        window._talkableq.push(['register_purchase', _talkable_data]);
      }
    },
    {},
    () => true,
  );

  if (!orderData) {
    return (
      <ErrorPage
        errorCode="404"
        description={ui('error.notFoundDescription')}
        hasHomeButton
      />
    );
  }

  return (
    <OrderTrackingResultContextProvider>
      <Grid>
        <CelebrationHeader
          firstName={fname || ''}
          logoUrl={isFleet ? LOGO_PATHS[userType] : LOGO_PATHS[USER_TYPE.NONE]}
          installerAddress={
            isOTS
              ? installerNameOTS
              : siteShipping?.cartShipping.installer?.company
          }
          orderNo={orderNo}
        />
        {siteShipping && siteBilling && order && (
          <GridItem gridColumnM="2/8" gridColumnL="2/14" gridColumnXL="3/13">
            <OrderSummarySection
              siteShipping={shippingData}
              siteBilling={billingData}
              order={order}
              siteCartSummaryData={cartSummaryData}
              cartInstallLocation={cartInstallLocation}
              isDealerTire={isDealerTire}
            />
          </GridItem>
        )}
        {!isWarehousePickup && (
          <GridItem gridColumnM="3/7" gridColumnL="2/7" gridColumnXL="3/7">
            {siteShipping && siteBilling && (
              <ShippingLocation
                siteShipping={shippingData || siteShipping}
                cartInstallLocation={cartInstallLocation}
              />
            )}
            {!lessThan.L &&
              !isOTS &&
              typeof emailMatchesExistingAccount !== 'undefined' &&
              customer && (
                <div ref={createAccountRef}>
                  <CreateAccount
                    email={email}
                    isLoggedIn={isLoggedIn}
                    emailMatchesExistingAccount={emailMatchesExistingAccount}
                    customer={customer}
                  />
                </div>
              )}
          </GridItem>
        )}
        {!lessThan.L && (
          <GridItem
            gridColumnM={'3/7'}
            gridColumnL={isWarehousePickup ? '2/14' : '8/14'}
            gridColumnXL={isWarehousePickup ? '5/11' : '8/14'}
          >
            <WhatHappens
              installerAddress={siteShipping?.cartShipping.installer?.company}
              installerLocation={
                siteShipping?.cartShipping.installer?.geolocation
              }
              cartAppointment={cartAppointment ?? undefined}
              isMobileInstall={
                siteShipping?.cartShipping.installer?.isMobileInstall
              }
              isWarehousePickup={isWarehousePickup}
              customerEmail={cartSummaryData?.siteCart?.email}
              warehouseAddress={warehouseAddress}
              purchaseDate={purchaseDate}
            />
          </GridItem>
        )}
      </Grid>
      {lessThan.L && (
        <div>
          {!isWarehousePickup && !isOTS && (
            <div css={styles.formBgContainer}>
              <Grid>
                <GridItem
                  gridColumnM="3/7"
                  gridColumnL="8/14"
                  gridColumnXL="8/13"
                >
                  <div ref={createAccountRef}>
                    {emailMatchesExistingAccount !== undefined && customer && (
                      <CreateAccount
                        email={email}
                        isLoggedIn={isLoggedIn}
                        emailMatchesExistingAccount={
                          emailMatchesExistingAccount
                        }
                        customer={customer}
                      />
                    )}
                  </div>
                </GridItem>
              </Grid>
            </div>
          )}
          <Grid>
            <GridItem gridColumnM="3/7" gridColumnL="8/14" gridColumnXL="8/13">
              <WhatHappens
                installerAddress={siteShipping?.cartShipping.installer?.company}
                installerLocation={
                  siteShipping?.cartShipping.installer?.geolocation
                }
                cartAppointment={cartAppointment ?? undefined}
                isMobileInstall={
                  siteShipping?.cartShipping.installer?.isMobileInstall
                }
                isWarehousePickup={isWarehousePickup}
                customerEmail={cartSummaryData?.siteCart?.email ?? ''}
                warehouseAddress={warehouseAddress}
                purchaseDate={siteCartOrder?.order?.orderDate}
              />
            </GridItem>
          </Grid>
        </div>
      )}
      {!isOTS && <Footer />}
    </OrderTrackingResultContextProvider>
  );
}

export default OrderConfirmationContainer;
